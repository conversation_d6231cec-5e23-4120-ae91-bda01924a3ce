# -*- coding: utf-8 -*-
"""
SimilarWeb数据采集器
用于采集SimilarWeb网站分析数据
需要提供有效的Cookie信息进行认证
"""

import requests
import json
import time
import csv
import os
from datetime import datetime
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs
import re


class SimilarWebSpider:
    def __init__(self, cookies: str = None):
        """
        初始化SimilarWeb爬虫

        Args:
            cookies: SimilarWeb的Cookie字符串
        """
        self.session = requests.Session()
        self.base_url = "https://pro.similarweb.com"

        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://pro.similarweb.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        if cookies:
            self.set_cookies(cookies)

    def set_cookies(self, cookies: str):
        """
        设置Cookie

        Args:
            cookies: Cookie字符串，格式如 "name1=value1; name2=value2"
        """
        cookie_dict = {}
        for cookie in cookies.split(';'):
            if '=' in cookie:
                name, value = cookie.strip().split('=', 1)
                cookie_dict[name] = value

        self.session.cookies.update(cookie_dict)
        print(f"已设置 {len(cookie_dict)} 个Cookie")

    def get_website_overview(self, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        获取网站概览数据

        Args:
            domain: 目标域名，如 "klingai.com"
            country: 国家代码，999表示全球
            duration: 时间范围，如 "1m", "3m", "6m"

        Returns:
            包含网站概览数据的字典
        """
        # 构建API URL
        api_url = f"{self.base_url}/api/websiteanalysis/overview/website-performance"

        params = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'webSource': 'Total'
        }

        try:
            response = self.session.get(api_url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            print(f"成功获取 {domain} 的概览数据")
            return data

        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return {}

    def get_traffic_overview(self, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        获取流量概览数据

        Args:
            domain: 目标域名
            country: 国家代码
            duration: 时间范围

        Returns:
            流量数据字典
        """
        api_url = f"{self.base_url}/api/websiteanalysis/traffic-overview"

        params = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'webSource': 'Total'
        }

        try:
            response = self.session.get(api_url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            print(f"成功获取 {domain} 的流量数据")
            return data

        except requests.exceptions.RequestException as e:
            print(f"获取流量数据失败: {e}")
            return {}

    def get_audience_overview(self, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        获取受众概览数据

        Args:
            domain: 目标域名
            country: 国家代码
            duration: 时间范围

        Returns:
            受众数据字典
        """
        api_url = f"{self.base_url}/api/websiteanalysis/audience-overview"

        params = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'webSource': 'Total'
        }

        try:
            response = self.session.get(api_url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            print(f"成功获取 {domain} 的受众数据")
            return data

        except requests.exceptions.RequestException as e:
            print(f"获取受众数据失败: {e}")
            return {}

    def parse_url_params(self, url: str) -> Dict:
        """
        解析URL参数

        Args:
            url: 完整的SimilarWeb URL

        Returns:
            解析出的参数字典
        """
        parsed = urlparse(url)
        params = parse_qs(parsed.query)

        # 从URL中提取域名 - 先从查询参数key中获取
        domain = None
        if 'key' in params and len(params['key']) > 0:
            domain = params['key'][0]

        # 从URL片段(hash)中提取参数
        fragment = parsed.fragment
        country = '999'  # 默认全球
        duration = '1m'  # 默认1个月

        if fragment:
            # 先分离查询参数部分
            if '?' in fragment:
                fragment_path, fragment_query = fragment.split('?', 1)
                # 解析fragment中的查询参数
                fragment_params = parse_qs(fragment_query)
                params.update(fragment_params)
            else:
                fragment_path = fragment

            # 解析类似 #/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m 的路径
            fragment_parts = fragment_path.split('/')
            if len(fragment_parts) >= 7:
                try:
                    # 通常格式: #/digitalsuite/websiteanalysis/overview/website-performance/*/country/duration
                    if len(fragment_parts) >= 6:
                        country = fragment_parts[-2]  # 倒数第二个是国家代码
                    if len(fragment_parts) >= 7:
                        duration = fragment_parts[-1]  # 最后一个是时间范围
                except (IndexError, ValueError):
                    pass

        # 如果没有key参数，尝试从URL路径中提取域名
        if not domain:
            path_parts = parsed.path.split('/')
            for part in path_parts:
                if '.' in part and not part.startswith('*') and part != 'website-performance':
                    domain = part
                    break

        result = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'webSource': 'Total'
        }

        # 更新从查询参数中获取的值
        for key, value in params.items():
            if isinstance(value, list) and len(value) > 0:
                result[key] = value[0]

        # 如果domain为空但有key参数，使用key作为domain
        if not result['domain'] and 'key' in result:
            result['domain'] = result['key']

        return result

    def collect_all_data(self, domain: str, country: str = "999", duration: str = "1m") -> Dict:
        """
        收集网站的所有可用数据

        Args:
            domain: 目标域名
            country: 国家代码
            duration: 时间范围

        Returns:
            包含所有数据的字典
        """
        print(f"开始收集 {domain} 的数据...")

        all_data = {
            'domain': domain,
            'country': country,
            'duration': duration,
            'collected_at': datetime.now().isoformat(),
            'overview': {},
            'traffic': {},
            'audience': {}
        }

        # 获取概览数据
        overview_data = self.get_website_overview(domain, country, duration)
        if overview_data:
            all_data['overview'] = overview_data

        # 等待一下避免请求过快
        time.sleep(1)

        # 获取流量数据
        traffic_data = self.get_traffic_overview(domain, country, duration)
        if traffic_data:
            all_data['traffic'] = traffic_data

        time.sleep(1)

        # 获取受众数据
        audience_data = self.get_audience_overview(domain, country, duration)
        if audience_data:
            all_data['audience'] = audience_data

        print(f"数据收集完成: {domain}")
        return all_data

    def save_to_json(self, data: Dict, filename: str = None):
        """
        保存数据到JSON文件

        Args:
            data: 要保存的数据
            filename: 文件名，如果不提供则自动生成
        """
        if not filename:
            domain = data.get('domain', 'unknown')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"similarweb_{domain}_{timestamp}.json"

        # 确保目录存在
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到: {filepath}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")

    def save_to_csv(self, data: Dict, filename: str = None):
        """
        保存数据到CSV文件

        Args:
            data: 要保存的数据
            filename: 文件名，如果不提供则自动生成
        """
        if not filename:
            domain = data.get('domain', 'unknown')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"similarweb_{domain}_{timestamp}.csv"

        # 确保目录存在
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # 写入基本信息
                writer.writerow(['域名', '国家', '时间范围', '收集时间'])
                writer.writerow([
                    data.get('domain', ''),
                    data.get('country', ''),
                    data.get('duration', ''),
                    data.get('collected_at', '')
                ])

                writer.writerow([])  # 空行

                # 写入概览数据
                if 'overview' in data and data['overview']:
                    writer.writerow(['=== 网站概览数据 ==='])
                    self._write_dict_to_csv(writer, data['overview'], prefix='概览')

                writer.writerow([])  # 空行

                # 写入流量数据
                if 'traffic' in data and data['traffic']:
                    writer.writerow(['=== 流量数据 ==='])
                    self._write_dict_to_csv(writer, data['traffic'], prefix='流量')

                writer.writerow([])  # 空行

                # 写入受众数据
                if 'audience' in data and data['audience']:
                    writer.writerow(['=== 受众数据 ==='])
                    self._write_dict_to_csv(writer, data['audience'], prefix='受众')

            print(f"CSV数据已保存到: {filepath}")
        except Exception as e:
            print(f"保存CSV文件失败: {e}")

    def _write_dict_to_csv(self, writer, data: Dict, prefix: str = '', level: int = 0):
        """
        递归写入字典数据到CSV

        Args:
            writer: CSV writer对象
            data: 要写入的数据
            prefix: 前缀
            level: 嵌套层级
        """
        indent = "  " * level

        for key, value in data.items():
            if isinstance(value, dict):
                writer.writerow([f"{indent}{prefix}_{key}", ""])
                self._write_dict_to_csv(writer, value, f"{prefix}_{key}", level + 1)
            elif isinstance(value, list):
                writer.writerow([f"{indent}{prefix}_{key}", f"列表({len(value)}项)"])
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        writer.writerow([f"{indent}  项目{i+1}", ""])
                        self._write_dict_to_csv(writer, item, f"{prefix}_{key}_项目{i+1}", level + 2)
                    else:
                        writer.writerow([f"{indent}  项目{i+1}", str(item)])
            else:
                writer.writerow([f"{indent}{prefix}_{key}", str(value)])


def main():
    """
    主函数 - 演示如何使用SimilarWebSpider
    """
    print("SimilarWeb数据采集器")
    print("=" * 50)

    # 这里需要替换为您的实际Cookie
    cookies = """
    请在这里粘贴您的SimilarWeb Cookie信息
    格式如: session_id=xxx; user_token=yyy; other_cookie=zzz
    """

    # 如果没有提供Cookie，提示用户
    if "请在这里粘贴" in cookies:
        print("⚠️  请先设置有效的Cookie信息!")
        print("1. 登录 https://pro.similarweb.com")
        print("2. 打开浏览器开发者工具 (F12)")
        print("3. 在Network标签页中找到任意请求")
        print("4. 复制Cookie信息并替换上面的cookies变量")
        print("5. 重新运行程序")
        return

    # 创建爬虫实例
    spider = SimilarWebSpider(cookies=cookies)

    # 目标URL
    target_url = "https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/*/999/1m?webSource=Total&key=klingai.com"

    # 解析URL参数
    params = spider.parse_url_params(target_url)
    print(f"解析的参数: {params}")

    # 如果URL中没有域名，使用默认域名
    domain = params.get('domain') or 'klingai.com'
    country = params.get('country', '999')
    duration = params.get('duration', '1m')

    print(f"目标域名: {domain}")
    print(f"国家代码: {country}")
    print(f"时间范围: {duration}")
    print("-" * 50)

    # 收集数据
    all_data = spider.collect_all_data(domain, country, duration)

    if all_data and any([all_data.get('overview'), all_data.get('traffic'), all_data.get('audience')]):
        # 保存数据
        spider.save_to_json(all_data)
        spider.save_to_csv(all_data)
        print("✅ 数据采集完成!")
    else:
        print("❌ 数据采集失败，请检查Cookie是否有效")


if __name__ == "__main__":
    main()