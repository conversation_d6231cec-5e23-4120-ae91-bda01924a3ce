# -*- coding: utf-8 -*-
"""
SimilarWeb爬虫配置文件
"""

# SimilarWeb Cookie配置
# 请在登录SimilarWeb后，从浏览器开发者工具中复制Cookie信息
SIMILARWEB_COOKIES = """
请在这里粘贴您的SimilarWeb Cookie信息
格式如: session_id=xxx; user_token=yyy; other_cookie=zzz
"""

# 请求配置
REQUEST_CONFIG = {
    'timeout': 30,  # 请求超时时间(秒)
    'retry_times': 3,  # 重试次数
    'retry_delay': 2,  # 重试间隔(秒)
    'request_delay': 1,  # 请求间隔(秒)
}

# 数据保存配置
SAVE_CONFIG = {
    'save_json': True,  # 是否保存JSON格式
    'save_csv': True,   # 是否保存CSV格式
    'data_dir': 'data', # 数据保存目录
}

# 默认参数
DEFAULT_PARAMS = {
    'country': '999',    # 默认国家代码(999=全球)
    'duration': '1m',    # 默认时间范围(1个月)
    'webSource': 'Total' # 默认数据源
}

# 支持的时间范围
SUPPORTED_DURATIONS = ['1m', '3m', '6m', '12m', '18m', '24m']

# 支持的国家代码(部分)
SUPPORTED_COUNTRIES = {
    '999': '全球',
    '840': '美国',
    '156': '中国',
    '392': '日本',
    '826': '英国',
    '276': '德国',
    '250': '法国',
    '124': '加拿大',
    '036': '澳大利亚',
    '410': '韩国',
}

# API端点配置
API_ENDPOINTS = {
    'overview': '/api/websiteanalysis/overview/website-performance',
    'traffic': '/api/websiteanalysis/traffic-overview',
    'audience': '/api/websiteanalysis/audience-overview',
    'competitors': '/api/websiteanalysis/competitors',
    'keywords': '/api/websiteanalysis/keywords',
    'referrals': '/api/websiteanalysis/referrals',
}
